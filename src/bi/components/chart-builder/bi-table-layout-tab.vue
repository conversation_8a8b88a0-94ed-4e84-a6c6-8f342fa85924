<script setup>
import { computed, defineAsyncComponent, onMounted, ref, watch } from 'vue';
import { useBiStore } from '~/bi/store/bi.store';

const bi_store = useBiStore();
const draggable = defineAsyncComponent(() => import('vuedraggable'));

const columns = ref([]);
const rows = ref([]);
const values = ref([]);
const is_dragging = ref(false);

function onDragEnd() {
  is_dragging.value = true;
  bi_store.table_preview_config.columns = columns.value;
  bi_store.table_preview_config.rows = rows.value;
  bi_store.table_preview_config.values = values.value;
  bi_store.table_preview_config.is_dirty = true;
  is_dragging.value = false;
}

watch(() => bi_store.table_preview_config, () => {
  if (is_dragging.value)
    return;
  columns.value = bi_store.table_preview_config.columns;
  rows.value = bi_store.table_preview_config.rows;
  values.value = bi_store.table_preview_config.values;
}, { immediate: true, deep: true });
</script>

<template>
  <div class="space-y-6">
    <!-- Columns Section -->
    <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-200">
      <h3 class="font-medium mb-3">
        Columns
      </h3>
      <draggable
        v-model="columns"
        :group="{ name: 'fields', pull: true, put: true }"
        item-key="key"
        handle=".move"
        class="min-h-[50px] space-y-3"
        @end="onDragEnd"
      >
        <template #item="{ element }">
          <div class="p-2 rounded-md border border-gray-700 group">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="pr-2 move cursor-move">
                  <IconHawkDragIcon class="text-gray-400 w-[12px] h-[12px]" />
                </div>
                <span class="text-sm text-gray-600">{{ element }}</span>
              </div>
              <div>
                <IconHawkDotsVertical class="w-4 h-4" />
              </div>
            </div>
          </div>
        </template>
      </draggable>
    </div>

    <!-- Rows Section -->
    <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-200">
      <h3 class="font-medium mb-3">
        Rows
      </h3>
      <draggable
        v-model="rows"
        :group="{ name: 'fields', pull: true, put: true }"
        item-key="key"
        handle=".move"
        class="min-h-[50px] space-y-3"
        @end="onDragEnd"
      >
        <template #item="{ element }">
          <div class="p-2 rounded-md border border-gray-700 group">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="pr-2 move cursor-move">
                  <IconHawkDragIcon class="text-gray-400 w-[12px] h-[12px]" />
                </div>
                <span class="text-sm text-gray-600">{{ element }}</span>
              </div>
              <div>
                <IconHawkDotsVertical class="w-4 h-4" />
              </div>
            </div>
          </div>
        </template>
      </draggable>
    </div>

    <!-- Values Section (internally draggable) -->
    <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-200">
      <h3 class="font-medium mb-3">
        Values
      </h3>
      <draggable
        v-model="values"
        :group="{ name: 'values', pull: false, put: false }"
        item-key="key"
        handle=".move"
        class="min-h-[50px] space-y-3"
        @end="onDragEnd"
      >
        <template #item="{ element }">
          <div class="p-2 rounded-md border border-gray-700 group">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="pr-2 move cursor-move">
                  <IconHawkDragIcon class="text-gray-400 w-[12px] h-[12px]" />
                </div>
                <span class="text-sm text-gray-600">{{ element }}</span>
              </div>
              <div>
                <IconHawkDotsVertical class="w-4 h-4" />
              </div>
            </div>
          </div>
        </template>
      </draggable>
    </div>
  </div>
</template>
